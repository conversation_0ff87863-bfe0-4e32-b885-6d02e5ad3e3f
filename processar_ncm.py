import pandas as pd

# Define o nome do arquivo de entrada e de saída
input_filename = "CPESC_ITEMFISCAL_202508261723-completo2.csv"
output_filename = "CPESC_ITEMFISCAL_final_com_pai.csv"

# Tenta carregar o arquivo CSV, tratando todas as colunas como strings, conforme solicitado.
try:
    df = pd.read_csv(input_filename, dtype=str)
except FileNotFoundError:
    print(f"Erro: O arquivo '{input_filename}' não foi encontrado.")
    print("Por favor, certifique-se de que o script está na mesma pasta que o arquivo CSV.")
    exit()

# Cria um conjunto (set) com todos os NCMs únicos para uma busca muito rápida.
# O .dropna() remove valores vazios que possam causar problemas.
all_ncms = set(df['NCM'].dropna())

def encontrar_pai_otimizado(ncm, ncms_set):
    """
    Encontra o NCM pai para um dado NCM verificando seus prefixos.
    Esta abordagem é muito mais rápida do que comparar com todos os outros NCMs.
    """
    # Garante que o ncm seja uma string para poder fatiar (slice)
    ncm = str(ncm)
    
    # Itera do prefixo mais longo possível para o mais curto
    for i in range(len(ncm) - 1, 0, -1):
        prefixo = ncm[:i]
        if prefixo in ncms_set:
            # O primeiro que encontrarmos será o mais longo, portanto, é o pai.
            return prefixo
    
    # Se nenhum prefixo for encontrado no conjunto, não há pai.
    return ""

# Aplica a função otimizada para criar a nova coluna "NCM_pai"
print("Processando a tabela... Isso pode levar alguns segundos.")
df['NCM_pai'] = df['NCM'].apply(lambda ncm: encontrar_pai_otimizado(ncm, all_ncms))

# Salva o DataFrame resultante em um novo arquivo CSV
df.to_csv(output_filename, index=False)

print("Processamento concluído com sucesso!")
print(f"O novo arquivo '{output_filename}' foi criado com a coluna 'NCM_pai'.")